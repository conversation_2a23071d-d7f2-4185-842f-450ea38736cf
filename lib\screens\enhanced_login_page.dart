import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';
import '../appstate.dart';
import '../services/whatsapp_auth_service.dart';
import '../services/secure_storage_service.dart';

class EnhancedLoginPage extends StatefulWidget {
  const EnhancedLoginPage({super.key});

  @override
  State<EnhancedLoginPage> createState() => _EnhancedLoginPageState();
}

class _EnhancedLoginPageState extends State<EnhancedLoginPage>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _verificationCodeController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _rememberMe = false;
  bool _isVerificationStep = false;
  bool _isForgotPassword = false;
  bool _isResetPassword = false;
  String _verificationPhoneNumber = '';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
    _checkRememberMe();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _verificationCodeController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _checkRememberMe() async {
    final hasRememberMe = await SecureStorageService.hasRememberMeData();
    if (hasRememberMe) {
      final credentials = await SecureStorageService.getRememberMeData();
      if (credentials != null) {
        setState(() {
          _phoneController.text = WhatsAppAuthService.getDisplayPhoneNumber(
              credentials['phoneNumber']!);
          _passwordController.text = credentials['password']!;
          _rememberMe = true;
        });
      }
    }
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(fontFamily: 'Tajawal'),
        ),
        backgroundColor: backgroundColor ?? Colors.teal[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Future<void> _sendVerificationCode() async {
    if (!WhatsAppAuthService.isValidEgyptianPhoneNumber(
        _phoneController.text)) {
      _showSnackBar('يرجى إدخال رقم هاتف مصري صحيح', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await WhatsAppAuthService.sendVerificationCode(
        phoneNumber: _phoneController.text,
        channel: 'whatsapp',
      );

      if (result['success']) {
        setState(() {
          _isVerificationStep = true;
          _verificationPhoneNumber = result['phoneNumber'];
        });
        _showSnackBar(result['message'], Colors.green);
      } else {
        _showSnackBar(result['message'], Colors.red);
      }
    } catch (e) {
      _showSnackBar('حدث خطأ في إرسال رمز التحقق', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _verifyCode() async {
    if (_verificationCodeController.text.length != 6) {
      _showSnackBar('يرجى إدخال رمز التحقق المكون من 6 أرقام', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await WhatsAppAuthService.verifyCode(
        phoneNumber: _verificationPhoneNumber,
        code: _verificationCodeController.text,
      );

      if (result['success']) {
        setState(() {
          _isVerificationStep = false;
          if (_isForgotPassword) {
            _isResetPassword = true;
          }
        });

        if (!_isForgotPassword) {
          _showSnackBar(
              'تم التحقق بنجاح! يمكنك الآن تسجيل الدخول', Colors.green);
        } else {
          _showSnackBar('تم التحقق بنجاح! يمكنك الآن إدخال كلمة المرور الجديدة',
              Colors.green);
        }
      } else {
        _showSnackBar(result['message'], Colors.red);
      }
    } catch (e) {
      _showSnackBar('حدث خطأ في التحقق من الرمز', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      await appState.login(
        _phoneController.text,
        _passwordController.text,
        null,
        rememberMe: _rememberMe,
      );

      if (appState.isLoggedIn) {
        _showSnackBar('تم تسجيل الدخول بنجاح', Colors.green);
      }
    } catch (e) {
      _showSnackBar('فشل في تسجيل الدخول: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _sendPasswordResetCode() async {
    if (!WhatsAppAuthService.isValidEgyptianPhoneNumber(
        _phoneController.text)) {
      _showSnackBar('يرجى إدخال رقم هاتف مصري صحيح', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await WhatsAppAuthService.sendPasswordResetCode(
        phoneNumber: _phoneController.text,
      );

      if (result['success']) {
        setState(() {
          _isVerificationStep = true;
          _isForgotPassword = true;
          _verificationPhoneNumber = result['phoneNumber'];
        });
        _showSnackBar(result['message'], Colors.green);
      } else {
        _showSnackBar(result['message'], Colors.red);
      }
    } catch (e) {
      _showSnackBar('حدث خطأ في إرسال رمز الاسترداد', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _resetPassword() async {
    if (_newPasswordController.text != _confirmPasswordController.text) {
      _showSnackBar('كلمات المرور غير متطابقة', Colors.red);
      return;
    }

    if (_newPasswordController.text.length < 6) {
      _showSnackBar('كلمة المرور يجب أن تكون 6 أحرف على الأقل', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await WhatsAppAuthService.resetPassword(
        phoneNumber: _verificationPhoneNumber,
        code: _verificationCodeController.text,
        newPassword: _newPasswordController.text,
      );

      if (result['success']) {
        setState(() {
          _isResetPassword = false;
          _isForgotPassword = false;
          _isVerificationStep = false;
        });
        _showSnackBar(result['message'], Colors.green);
        _clearForm();
      } else {
        _showSnackBar(result['message'], Colors.red);
      }
    } catch (e) {
      _showSnackBar('حدث خطأ في إعادة تعيين كلمة المرور', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _clearForm() {
    _phoneController.clear();
    _passwordController.clear();
    _verificationCodeController.clear();
    _newPasswordController.clear();
    _confirmPasswordController.clear();
    setState(() {
      _rememberMe = false;
      _isVerificationStep = false;
      _isForgotPassword = false;
      _isResetPassword = false;
    });
  }

  void _goBack() {
    setState(() {
      if (_isResetPassword) {
        _isResetPassword = false;
      } else if (_isVerificationStep) {
        _isVerificationStep = false;
        _isForgotPassword = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[200]!],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _buildCurrentStep(),
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentStep() {
    if (_isResetPassword) {
      return _buildResetPasswordStep();
    } else if (_isVerificationStep) {
      return _buildVerificationStep();
    } else {
      return _buildLoginStep();
    }
  }

  Widget _buildLoginStep() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo and Title
              ZoomIn(
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.phone_android,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'تسجيل الدخول',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'مرحباً بك مرة أخرى',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 40),

              // Phone Number Field
              SlideInLeft(
                child: _buildNeumorphicField(
                  child: TextFormField(
                    controller: _phoneController,
                    decoration: const InputDecoration(
                      labelText: 'رقم الهاتف',
                      hintText: '01xxxxxxxxx',
                      prefixIcon: Icon(Icons.phone, color: Colors.white70),
                      border: InputBorder.none,
                      labelStyle: TextStyle(
                          color: Colors.white70, fontFamily: 'Tajawal'),
                      hintStyle: TextStyle(
                          color: Colors.white54, fontFamily: 'Tajawal'),
                    ),
                    style: const TextStyle(
                        color: Colors.white, fontFamily: 'Tajawal'),
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم الهاتف';
                      }
                      if (!WhatsAppAuthService.isValidEgyptianPhoneNumber(
                          value)) {
                        return 'يرجى إدخال رقم هاتف مصري صحيح';
                      }
                      return null;
                    },
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Password Field
              SlideInRight(
                child: _buildNeumorphicField(
                  child: TextFormField(
                    controller: _passwordController,
                    decoration: InputDecoration(
                      labelText: 'كلمة المرور',
                      prefixIcon: const Icon(Icons.lock, color: Colors.white70),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isPasswordVisible
                              ? Icons.visibility
                              : Icons.visibility_off,
                          color: Colors.white70,
                        ),
                        onPressed: () {
                          setState(() {
                            _isPasswordVisible = !_isPasswordVisible;
                          });
                        },
                      ),
                      border: InputBorder.none,
                      labelStyle: const TextStyle(
                          color: Colors.white70, fontFamily: 'Tajawal'),
                    ),
                    style: const TextStyle(
                        color: Colors.white, fontFamily: 'Tajawal'),
                    obscureText: !_isPasswordVisible,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال كلمة المرور';
                      }
                      if (value.length < 6) {
                        return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                      }
                      return null;
                    },
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Remember Me Checkbox
              SlideInUp(
                child: Row(
                  children: [
                    Checkbox(
                      value: _rememberMe,
                      onChanged: (value) {
                        setState(() {
                          _rememberMe = value ?? false;
                        });
                      },
                      activeColor: Colors.white,
                      checkColor: Colors.teal[700],
                    ),
                    const Text(
                      'تذكرني',
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'Tajawal',
                        fontSize: 16,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: _sendPasswordResetCode,
                      child: const Text(
                        'نسيت كلمة المرور؟',
                        style: TextStyle(
                          color: Colors.white70,
                          fontFamily: 'Tajawal',
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Login Button
              ZoomIn(
                child: _isLoading
                    ? const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      )
                    : _buildGradientButton(
                        text: 'تسجيل الدخول',
                        onPressed: _login,
                        icon: Icons.login,
                      ),
              ),
              const SizedBox(height: 20),

              // WhatsApp Verification Button
              SlideInUp(
                child: _buildOutlineButton(
                  text: 'التحقق عبر الواتساب',
                  onPressed: _sendVerificationCode,
                  icon: Icons.message,
                ),
              ),
              const SizedBox(height: 20),

              // Guest Login
              SlideInUp(
                child: TextButton(
                  onPressed: () async {
                    final appState =
                        Provider.of<AppState>(context, listen: false);
                    await appState.loginAsGuest();
                  },
                  child: const Text(
                    'تسجيل الدخول كزائر',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Tajawal',
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVerificationStep() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Back Button
            Align(
              alignment: Alignment.topRight,
              child: IconButton(
                onPressed: _goBack,
                icon: const Icon(Icons.arrow_back, color: Colors.white),
              ),
            ),
            const SizedBox(height: 20),

            // WhatsApp Icon
            ZoomIn(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.message,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 30),

            // Title
            const Text(
              'التحقق من رقم الهاتف',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontFamily: 'Tajawal',
              ),
            ),
            const SizedBox(height: 10),

            // Subtitle
            Text(
              'تم إرسال رمز التحقق إلى\n${WhatsAppAuthService.getDisplayPhoneNumber(_verificationPhoneNumber)}',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white70,
                fontFamily: 'Tajawal',
              ),
            ),
            const SizedBox(height: 40),

            // Verification Code Field
            SlideInUp(
              child: _buildNeumorphicField(
                child: TextFormField(
                  controller: _verificationCodeController,
                  decoration: const InputDecoration(
                    labelText: 'رمز التحقق',
                    hintText: '123456',
                    prefixIcon: Icon(Icons.security, color: Colors.white70),
                    border: InputBorder.none,
                    labelStyle:
                        TextStyle(color: Colors.white70, fontFamily: 'Tajawal'),
                    hintStyle:
                        TextStyle(color: Colors.white54, fontFamily: 'Tajawal'),
                  ),
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal'),
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  maxLength: 6,
                ),
              ),
            ),
            const SizedBox(height: 30),

            // Verify Button
            ZoomIn(
              child: _isLoading
                  ? const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    )
                  : _buildGradientButton(
                      text: 'تأكيد الرمز',
                      onPressed: _verifyCode,
                      icon: Icons.check,
                    ),
            ),
            const SizedBox(height: 20),

            // Resend Code
            TextButton(
              onPressed: () {
                if (_isForgotPassword) {
                  _sendPasswordResetCode();
                } else {
                  _sendVerificationCode();
                }
              },
              child: const Text(
                'إعادة إرسال الرمز',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Tajawal',
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResetPasswordStep() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Back Button
            Align(
              alignment: Alignment.topRight,
              child: IconButton(
                onPressed: _goBack,
                icon: const Icon(Icons.arrow_back, color: Colors.white),
              ),
            ),
            const SizedBox(height: 20),

            // Lock Icon
            ZoomIn(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.lock_reset,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 30),

            // Title
            const Text(
              'إعادة تعيين كلمة المرور',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontFamily: 'Tajawal',
              ),
            ),
            const SizedBox(height: 10),

            // Subtitle
            const Text(
              'يرجى إدخال كلمة المرور الجديدة',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
                fontFamily: 'Tajawal',
              ),
            ),
            const SizedBox(height: 40),

            // New Password Field
            SlideInLeft(
              child: _buildNeumorphicField(
                child: TextFormField(
                  controller: _newPasswordController,
                  decoration: const InputDecoration(
                    labelText: 'كلمة المرور الجديدة',
                    prefixIcon: Icon(Icons.lock, color: Colors.white70),
                    border: InputBorder.none,
                    labelStyle:
                        TextStyle(color: Colors.white70, fontFamily: 'Tajawal'),
                  ),
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal'),
                  obscureText: true,
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Confirm Password Field
            SlideInRight(
              child: _buildNeumorphicField(
                child: TextFormField(
                  controller: _confirmPasswordController,
                  decoration: const InputDecoration(
                    labelText: 'تأكيد كلمة المرور',
                    prefixIcon: Icon(Icons.lock_outline, color: Colors.white70),
                    border: InputBorder.none,
                    labelStyle:
                        TextStyle(color: Colors.white70, fontFamily: 'Tajawal'),
                  ),
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal'),
                  obscureText: true,
                ),
              ),
            ),
            const SizedBox(height: 30),

            // Reset Button
            ZoomIn(
              child: _isLoading
                  ? const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    )
                  : _buildGradientButton(
                      text: 'تغيير كلمة المرور',
                      onPressed: _resetPassword,
                      icon: Icons.save,
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNeumorphicField({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
      child: child,
    );
  }

  Widget _buildGradientButton({
    required String text,
    required VoidCallback onPressed,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      height: 55,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.white.withOpacity(0.7)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.teal[700], size: 24),
              const SizedBox(width: 10),
              Text(
                text,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal[700],
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOutlineButton({
    required String text,
    required VoidCallback onPressed,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      height: 55,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white, width: 2),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.white, size: 24),
              const SizedBox(width: 10),
              Text(
                text,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
