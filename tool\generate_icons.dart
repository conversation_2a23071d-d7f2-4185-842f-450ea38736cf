import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// أداة لتوليد أيقونات التطبيق بأحجام مختلفة
class IconGenerator {
  static const Map<String, List<int>> iconSizes = {
    'android': [
      36, 48, 72, 96, 144, 192, // mdpi, hdpi, xhdpi, xxhdpi, xxxhdpi
    ],
    'ios': [
      20, 29, 40, 58, 60, 76, 80, 87, 120, 152, 167, 180, 1024
    ],
  };

  /// توليد أيقونة مخصصة لسفينة نوح
  static Widget buildFulkIcon({double size = 512}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF87CEEB), // Sky blue
            Color(0xFFB0E0E6), // Light blue
          ],
        ),
        border: Border.all(
          color: const Color(0xFF2E8B57),
          width: size * 0.015,
        ),
      ),
      child: ClipOval(
        child: Stack(
          children: [
            // الخلفية والماء
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              height: size * 0.3,
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF4682B4),
                      Color(0xFF1E90FF),
                    ],
                  ),
                ),
              ),
            ),
            
            // جسم السفينة
            Positioned(
              bottom: size * 0.15,
              left: size * 0.15,
              right: size * 0.15,
              height: size * 0.12,
              child: Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF8B4513),
                      Color(0xFFA0522D),
                      Color(0xFF654321),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(size * 0.02),
                  border: Border.all(
                    color: const Color(0xFF654321),
                    width: size * 0.004,
                  ),
                ),
              ),
            ),
            
            // الصاري الرئيسي
            Positioned(
              bottom: size * 0.27,
              left: size * 0.49,
              width: size * 0.02,
              height: size * 0.35,
              child: Container(
                color: const Color(0xFF8B4513),
              ),
            ),
            
            // الشراع الرئيسي
            Positioned(
              bottom: size * 0.35,
              left: size * 0.3,
              right: size * 0.3,
              height: size * 0.25,
              child: Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Color(0xFFF5F5DC),
                      Color(0xFFFFFFFF),
                      Color(0xFFE6E6FA),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(size * 0.02),
                  border: Border.all(
                    color: const Color(0xFFD3D3D3),
                    width: size * 0.002,
                  ),
                ),
              ),
            ),
            
            // العلم الإسلامي
            Positioned(
              top: size * 0.15,
              left: size * 0.51,
              width: size * 0.08,
              height: size * 0.06,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF228B22),
                  borderRadius: BorderRadius.circular(size * 0.005),
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // الهلال
                      Icon(
                        Icons.brightness_2,
                        color: const Color(0xFFFFD700),
                        size: size * 0.02,
                      ),
                      SizedBox(width: size * 0.005),
                      // النجمة
                      Icon(
                        Icons.star,
                        color: const Color(0xFFFFD700),
                        size: size * 0.015,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // الشمس
            Positioned(
              top: size * 0.1,
              right: size * 0.1,
              child: Container(
                width: size * 0.1,
                height: size * 0.1,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFFFFD700),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFFFD700).withOpacity(0.5),
                      blurRadius: size * 0.02,
                      spreadRadius: size * 0.005,
                    ),
                  ],
                ),
              ),
            ),
            
            // طيور في السماء
            Positioned(
              top: size * 0.15,
              left: size * 0.2,
              child: CustomPaint(
                size: Size(size * 0.1, size * 0.05),
                painter: BirdPainter(size: size),
              ),
            ),
            
            Positioned(
              top: size * 0.12,
              right: size * 0.25,
              child: CustomPaint(
                size: Size(size * 0.08, size * 0.04),
                painter: BirdPainter(size: size),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// رسام مخصص للطيور
class BirdPainter extends CustomPainter {
  final double size;
  
  BirdPainter({required this.size});
  
  @override
  void paint(Canvas canvas, Size canvasSize) {
    final paint = Paint()
      ..color = const Color(0xFF696969)
      ..strokeWidth = size * 0.003
      ..style = PaintingStyle.stroke;
    
    final path = Path();
    path.moveTo(0, canvasSize.height * 0.5);
    path.quadraticBezierTo(
      canvasSize.width * 0.25, 0,
      canvasSize.width * 0.5, canvasSize.height * 0.5,
    );
    path.quadraticBezierTo(
      canvasSize.width * 0.75, 0,
      canvasSize.width, canvasSize.height * 0.5,
    );
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// دالة مساعدة لحفظ الأيقونة كملف PNG
Future<void> saveIconAsPng(Widget icon, String path, int size) async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  
  final widget = RepaintBoundary(
    child: SizedBox(
      width: size.toDouble(),
      height: size.toDouble(),
      child: icon,
    ),
  );
  
  // تحويل Widget إلى صورة
  final RenderRepaintBoundary boundary = RenderRepaintBoundary();
  final pipelineOwner = PipelineOwner();
  final buildOwner = BuildOwner(focusManager: FocusManager());
  
  final renderView = RenderView(
    configuration: const ViewConfiguration(
      size: Size(512, 512),
      devicePixelRatio: 1.0,
    ),
    window: WidgetsBinding.instance.window,
  );
  
  pipelineOwner.rootNode = renderView;
  renderView.prepareInitialFrame();
  
  final rootElement = RenderObjectToWidgetAdapter<RenderBox>(
    container: boundary,
    child: widget,
  ).attachToRenderTree(buildOwner);
  
  buildOwner.buildScope(rootElement);
  buildOwner.finalizeTree();
  pipelineOwner.flushLayout();
  pipelineOwner.flushCompositingBits();
  pipelineOwner.flushPaint();
  
  final image = await boundary.toImage(pixelRatio: size / 512);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  final pngBytes = byteData!.buffer.asUint8List();
  
  final file = File(path);
  await file.create(recursive: true);
  await file.writeAsBytes(pngBytes);
}

/// دالة رئيسية لتوليد جميع الأيقونات
Future<void> generateAllIcons() async {
  print('🎨 بدء توليد أيقونات تطبيق فلك...');
  
  // إنشاء المجلدات المطلوبة
  await Directory('android/app/src/main/res/mipmap-mdpi').create(recursive: true);
  await Directory('android/app/src/main/res/mipmap-hdpi').create(recursive: true);
  await Directory('android/app/src/main/res/mipmap-xhdpi').create(recursive: true);
  await Directory('android/app/src/main/res/mipmap-xxhdpi').create(recursive: true);
  await Directory('android/app/src/main/res/mipmap-xxxhdpi').create(recursive: true);
  await Directory('ios/Runner/Assets.xcassets/AppIcon.appiconset').create(recursive: true);
  
  // توليد أيقونات Android
  final androidSizes = [
    {'size': 48, 'folder': 'mipmap-mdpi'},
    {'size': 72, 'folder': 'mipmap-hdpi'},
    {'size': 96, 'folder': 'mipmap-xhdpi'},
    {'size': 144, 'folder': 'mipmap-xxhdpi'},
    {'size': 192, 'folder': 'mipmap-xxxhdpi'},
  ];
  
  for (final config in androidSizes) {
    final icon = IconGenerator.buildFulkIcon(size: config['size']! as double);
    final path = 'android/app/src/main/res/${config['folder']}/ic_launcher.png';
    await saveIconAsPng(icon, path, config['size']! as int);
    print('✅ تم إنشاء: $path');
  }
  
  // توليد أيقونات iOS
  final iosSizes = [60, 76, 83.5, 120, 152, 167, 180, 1024];
  
  for (final size in iosSizes) {
    final icon = IconGenerator.buildFulkIcon(size: size.toDouble());
    final path = 'ios/Runner/Assets.xcassets/AppIcon.appiconset/Icon-${size}x$size.png';
    await saveIconAsPng(icon, path, size.toInt());
    print('✅ تم إنشاء: $path');
  }
  
  print('🎉 تم الانتهاء من توليد جميع الأيقونات بنجاح!');
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await generateAllIcons();
}
